package crawl

import (
	"context"
	"testing"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/test/gtest"
)

func TestAntiBotConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		crawler := New().(*sCrawler)

		// 測試載入反爬蟲配置
		config := crawler.loadAntiBotConfig(ctx)
		t.AssertNE(config, nil)
		t.AssertGT(config.MinDelay, 0)
		t.AssertGT(config.MaxDelay, 0)
		t.AssertGT(config.MaxRetries, 0)
		t.AssertGT(len(config.UserAgents), 0)
	})
}

func TestGetRandomUserAgent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		crawler := New().(*sCrawler)
		config := &AntiBotConfig{
			UserAgents: []string{
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
			},
		}

		// 測試隨機獲取 User-Agent
		userAgent1 := crawler.getRandomUserAgent(config)
		userAgent2 := crawler.getRandomUserAgent(config)

		t.AssertNE(userAgent1, "")
		t.AssertNE(userAgent2, "")
		t.AssertIN(userAgent1, config.UserAgents)
		t.AssertIN(userAgent2, config.UserAgents)
	})
}

func TestRandomDelay(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		crawler := New().(*sCrawler)
		config := &AntiBotConfig{
			MinDelay: 100,
			MaxDelay: 200,
		}

		// 測試隨機延遲功能
		start := time.Now()
		crawler.randomDelay(ctx, config)
		elapsed := time.Since(start)

		// 延遲應該在配置範圍內
		t.AssertGE(elapsed.Milliseconds(), int64(config.MinDelay))
		t.AssertLE(elapsed.Milliseconds(), int64(config.MaxDelay+50)) // 允許一些誤差
	})
}

func TestExtractDomainUrl(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		crawler := New().(*sCrawler)

		// 測試域名提取功能
		testCases := []struct {
			input    string
			expected string
		}{
			{"https://www.example.com/path", "www.example.com"},
			{"http://example.com:8080/path", "example.com"},
			{"example.com", "example.com"},
			{"www.example.com/path?query=1", "www.example.com"},
		}

		for _, tc := range testCases {
			result, err := crawler.extractDomainUrl(tc.input)
			t.AssertNil(err)
			t.AssertEQ(result, tc.expected)
		}
	})
}

func TestAntiBotConfigFromConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 設置測試配置
		g.Cfg().GetAdapter().(*gcfg.AdapterFile).SetContent(`
system:
  crawling:
    anti_bot:
      min_delay: 2000
      max_delay: 5000
      max_retries: 5
      retry_delay: 10000
      user_agents:
        - "Test User Agent 1"
        - "Test User Agent 2"
      proxy:
        enabled: true
        servers:
          - "http://proxy1:8080"
          - "http://proxy2:8080"
`)

		crawler := New().(*sCrawler)
		config := crawler.loadAntiBotConfig(ctx)

		// 驗證配置是否正確載入
		t.AssertEQ(config.MinDelay, 2000)
		t.AssertEQ(config.MaxDelay, 5000)
		t.AssertEQ(config.MaxRetries, 5)
		t.AssertEQ(config.RetryDelay, 10000)
		t.AssertEQ(len(config.UserAgents), 2)
		t.AssertEQ(config.UserAgents[0], "Test User Agent 1")
		t.AssertEQ(config.ProxyConfig.Enabled, true)
		t.AssertEQ(len(config.ProxyConfig.Servers), 2)
	})
}

// TestIsWebContent 測試內容類型檢查功能
func TestIsWebContent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		crawler := New().(*sCrawler)

		// 測試案例
		testCases := []struct {
			contentType string
			expected    bool
			description string
		}{
			// 應該被識別為網頁內容的類型
			{"text/html", true, "HTML 內容"},
			{"text/html; charset=utf-8", true, "HTML 內容帶字符集"},
			{"application/xhtml+xml", true, "XHTML 內容"},
			{"text/plain", true, "純文本內容"},
			{"text/xml", true, "XML 內容"},
			{"application/xml", true, "應用程序 XML"},

			// 應該被跳過的非網頁內容類型
			{"application/zip", false, "ZIP 壓縮檔"},
			{"application/x-zip-compressed", false, "ZIP 壓縮檔（另一種格式）"},
			{"application/pdf", false, "PDF 文件"},
			{"application/octet-stream", false, "二進制流"},
			{"image/jpeg", false, "JPEG 圖片"},
			{"image/png", false, "PNG 圖片"},
			{"video/mp4", false, "MP4 視頻"},
			{"audio/mpeg", false, "MP3 音頻"},
			{"application/msword", false, "Word 文檔"},
			{"application/vnd.ms-excel", false, "Excel 文檔"},
			{"application/vnd.openxmlformats-officedocument.wordprocessingml.document", false, "DOCX 文檔"},

			// 邊界案例
			{"", false, "空字符串"},
			{"TEXT/HTML", true, "大寫 HTML"},
			{"  text/html  ", true, "帶空格的 HTML"},
			{"unknown/type", true, "未知類型（默認允許）"},
		}

		for _, tc := range testCases {
			result := crawler.isWebContent(tc.contentType)
			if result != tc.expected {
				t.Errorf("isWebContent(%q) = %v, expected %v (%s)",
					tc.contentType, result, tc.expected, tc.description)
			}
		}
	})
}

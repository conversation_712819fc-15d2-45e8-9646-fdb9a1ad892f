# https://goframe.org/docs/web/server-config-file-template
server:
  address: ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"

# https://goframe.org/docs/core/glog-config
logger:
  level: "all"
  stdout: true

# system
system:
  temp_location: ./tmp
  crawling:
    max_depth: 10
    max_pages: 50
    # 反爬蟲配置
    anti_bot:
      # 請求間隔配置（毫秒）
      min_delay: 1000
      max_delay: 3000
      # 重試配置
      max_retries: 3
      retry_delay: 5000
      # User-Agent 池
      user_agents:
        - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      # 代理配置（可選）
      proxy:
        enabled: false
        servers: []
        # 示例：
        # - "http://proxy1:8080"
        # - "http://proxy2:8080"

data_service:
  name: dsh.svc
  scheme: http


# message queue
rabbitMQ:
  url: amqp://guest:guest@localhost:5672/

storage:
  # gcs: google cloud storage
  # localFS: local storage
  provider: gcs
  # local file system parameters
  localFS:
    path: ./files
  # google cloud storage parameters
  gcs:
    credential_file: ./key.json
    bucket: dev-123

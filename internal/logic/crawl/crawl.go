package crawl

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/crawl"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/model/website"
	"assetManagementService/internal/service"
	"context"
	"fmt"
	"math/rand"
	"net/url"
	"os"
	"strings"
	"time"

	md "github.com/<PERSON>mann/html-to-markdown"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/playwright-community/playwright-go"
)

// ErrNonWebContent 表示 URL 指向非網頁內容，應該跳過
var ErrNonWebContent = fmt.Errorf("non-web content detected, skipping URL")

// AntiBotConfig 反爬蟲配置結構體
type AntiBotConfig struct {
	MinDelay    int      `json:"min_delay"`   // 最小請求間隔（毫秒）
	MaxDelay    int      `json:"max_delay"`   // 最大請求間隔（毫秒）
	MaxRetries  int      `json:"max_retries"` // 最大重試次數
	RetryDelay  int      `json:"retry_delay"` // 重試間隔（毫秒）
	UserAgents  []string `json:"user_agents"` // User-Agent 池
	ProxyConfig struct {
		Enabled bool     `json:"enabled"` // 是否啟用代理
		Servers []string `json:"servers"` // 代理服務器列表
	} `json:"proxy"`
}

func init() {
	err := playwright.Install()
	if err != nil {
		panic(err)
	}

	service.RegisterCrawler(New())
}
func New() service.ICrawler {
	s := &sCrawler{
		workerPool: grpool.New(),
	}
	var err error
	s.playWright, err = playwright.Run()
	if err != nil {
		panic(err)
	}
	return s
}

type sCrawler struct {
	playWright *playwright.Playwright
	workerPool *grpool.Pool
}

// loadAntiBotConfig 載入反爬蟲配置
func (s *sCrawler) loadAntiBotConfig(ctx context.Context) *AntiBotConfig {
	config := &AntiBotConfig{
		MinDelay:   1000,
		MaxDelay:   3000,
		MaxRetries: 3,
		RetryDelay: 5000,
		UserAgents: []string{
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		},
	}

	// 從配置文件載入設置
	if minDelay, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.min_delay"); !minDelay.IsEmpty() {
		config.MinDelay = minDelay.Int()
	}
	if maxDelay, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.max_delay"); !maxDelay.IsEmpty() {
		config.MaxDelay = maxDelay.Int()
	}
	if maxRetries, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.max_retries"); !maxRetries.IsEmpty() {
		config.MaxRetries = maxRetries.Int()
	}
	if retryDelay, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.retry_delay"); !retryDelay.IsEmpty() {
		config.RetryDelay = retryDelay.Int()
	}
	if userAgents, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.user_agents"); !userAgents.IsEmpty() {
		config.UserAgents = userAgents.Strings()
	}
	if proxyEnabled, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.proxy.enabled"); !proxyEnabled.IsEmpty() {
		config.ProxyConfig.Enabled = proxyEnabled.Bool()
	}
	if proxyServers, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.proxy.servers"); !proxyServers.IsEmpty() {
		config.ProxyConfig.Servers = proxyServers.Strings()
	}

	return config
}

// getRandomUserAgent 隨機獲取 User-Agent
func (s *sCrawler) getRandomUserAgent(config *AntiBotConfig) string {
	if len(config.UserAgents) == 0 {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}
	return config.UserAgents[rand.Intn(len(config.UserAgents))]
}

// randomDelay 隨機延遲
func (s *sCrawler) randomDelay(ctx context.Context, config *AntiBotConfig) {
	if config.MinDelay <= 0 && config.MaxDelay <= 0 {
		return
	}

	minDelay := config.MinDelay
	maxDelay := config.MaxDelay
	if minDelay > maxDelay {
		minDelay, maxDelay = maxDelay, minDelay
	}

	delay := minDelay
	if maxDelay > minDelay {
		delay = minDelay + rand.Intn(maxDelay-minDelay)
	}

	if delay > 0 {
		s.logger().Debugf(ctx, "Random delay: %d ms", delay)
		time.Sleep(time.Duration(delay) * time.Millisecond)
	}
}

func (s *sCrawler) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogCrawl)
}

// isWebContent 檢查 Content-Type 是否為可爬取的網頁內容
func (s *sCrawler) isWebContent(contentType string) bool {
	if contentType == "" {
		return false
	}

	// 轉換為小寫以便比較
	contentType = strings.ToLower(strings.TrimSpace(contentType))

	// 支持的網頁內容類型
	webContentTypes := []string{
		"text/html",
		"application/xhtml+xml",
		"text/plain",
		"text/xml",
		"application/xml",
	}

	// 檢查是否匹配任何支持的類型
	for _, webType := range webContentTypes {
		if strings.HasPrefix(contentType, webType) {
			return true
		}
	}

	// 排除的非網頁內容類型
	nonWebContentTypes := []string{
		"application/zip",
		"application/x-zip-compressed",
		"application/pdf",
		"application/octet-stream",
		"image/",
		"video/",
		"audio/",
		"application/msword",
		"application/vnd.ms-excel",
		"application/vnd.ms-powerpoint",
		"application/vnd.openxmlformats",
	}

	// 檢查是否為非網頁內容
	for _, nonWebType := range nonWebContentTypes {
		if strings.HasPrefix(contentType, nonWebType) {
			return false
		}
	}

	// 默認情況下，如果不確定則認為是網頁內容
	return true
}

// extractDomainUrl extracts the domain from a URL string
// It ensures the URL has a https:// prefix and removes any port number
func (s *sCrawler) extractDomainUrl(urlString string) (string, error) {
	// Validate input
	if urlString == "" {
		return "", fmt.Errorf("empty URL string")
	}

	// Ensure URL has https:// prefix
	if !gstr.HasPrefix(urlString, "https://") && !gstr.HasPrefix(urlString, "http://") {
		urlString = "https://" + urlString
	}

	// Parse the URL
	parsedURL, err := url.Parse(urlString)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	// Extract the host
	host := parsedURL.Host
	if host == "" {
		return "", fmt.Errorf("URL has no host: %s", urlString)
	}

	// Remove port number if present
	if i := strings.IndexByte(host, ':'); i >= 0 {
		host = host[:i]
	}

	return host, nil
}

// createPage creates a new page in the browser context with anti-bot settings
func (s *sCrawler) createPage(ctx context.Context, pageCtx playwright.BrowserContext, pageURL string, config *AntiBotConfig) (playwright.Page, error) {
	page, err := pageCtx.NewPage()
	if err != nil {
		s.logger().Errorf(ctx, "Error creating page for %s: %v", pageURL, err)
		return nil, fmt.Errorf("failed to create page: %w", err)
	}

	// 設置額外的 HTTP headers 來模擬真實瀏覽器
	headers := map[string]string{
		"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
		"Accept-Language":           "zh-TW,zh;q=0.9,en;q=0.8,zh-CN;q=0.7",
		"Accept-Encoding":           "gzip, deflate, br",
		"Cache-Control":             "max-age=0",
		"Sec-Fetch-Dest":            "document",
		"Sec-Fetch-Mode":            "navigate",
		"Sec-Fetch-Site":            "none",
		"Sec-Fetch-User":            "?1",
		"Upgrade-Insecure-Requests": "1",
	}

	// 設置 headers
	if err := page.SetExtraHTTPHeaders(headers); err != nil {
		s.logger().Errorf(ctx, "Failed to set extra HTTP headers for %s: %v", pageURL, err)
	}

	// 記錄使用的配置信息
	s.logger().Debugf(ctx, "Created page for %s with anti-bot config (retries: %d, delay: %d-%d ms)",
		pageURL, config.MaxRetries, config.MinDelay, config.MaxDelay)

	return page, nil
}

// navigateToURL navigates to the specified URL with retry mechanism for anti-bot protection
func (s *sCrawler) navigateToURL(ctx context.Context, page playwright.Page, pageURL string, config *AntiBotConfig) error {
	var lastErr error

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		if attempt > 0 {
			s.logger().Infof(ctx, "Retrying navigation to %s (attempt %d/%d)", pageURL, attempt, config.MaxRetries)
			time.Sleep(time.Duration(config.RetryDelay) * time.Millisecond)
		}

		resp, err := page.Goto(pageURL, playwright.PageGotoOptions{
			WaitUntil: playwright.WaitUntilStateNetworkidle,
			Timeout:   playwright.Float(30000), // 30 seconds timeout
		})

		if err != nil {
			lastErr = fmt.Errorf("failed to navigate to URL: %w", err)
			s.logger().Errorf(ctx, "Error navigating to %s (attempt %d): %v", pageURL, attempt+1, err)
			continue
		}

		// Check if the page was loaded successfully
		statusCode := resp.Status()
		if statusCode >= 400 {
			lastErr = fmt.Errorf("page returned status code %d", statusCode)
			s.logger().Errorf(ctx, "Error: %s returned status code %d (attempt %d)", pageURL, statusCode, attempt+1)

			// 對於 403/429 錯誤，特別處理
			if statusCode == 403 || statusCode == 429 {
				s.logger().Infof(ctx, "Detected anti-bot response (status %d), will retry with different settings", statusCode)
				continue
			}

			// 對於其他 4xx/5xx 錯誤，如果不是最後一次嘗試則繼續重試
			if attempt < config.MaxRetries {
				continue
			}
		} else {
			// 成功載入頁面，檢查內容類型
			s.logger().Debugf(ctx, "Successfully navigated to %s (status %d)", pageURL, statusCode)

			// 獲取響應的 Content-Type
			headers := resp.Headers()
			contentType := ""
			if headers != nil {
				if ct, exists := headers["content-type"]; exists {
					contentType = ct
				}
			}

			// 檢查是否為網頁內容
			if !s.isWebContent(contentType) {
				s.logger().Infof(ctx, "Skipping non-web content: %s (Content-Type: %s)", pageURL, contentType)
				return ErrNonWebContent
			}

			s.logger().Debugf(ctx, "Content-Type verified as web content: %s", contentType)
			return nil
		}
	}

	return lastErr
}

// getPageContent gets the HTML content of the page and converts it to Markdown
func (s *sCrawler) getPageContent(ctx context.Context, page playwright.Page, pageURL string) (string, error) {
	// Get the HTML content
	content, err := page.Content()
	if err != nil {
		s.logger().Errorf(ctx, "Error getting content from %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to get page content: %w", err)
	}

	// Convert HTML to Markdown
	converter := md.NewConverter("", true, nil)
	markdown, err := converter.ConvertString(content)
	if err != nil {
		s.logger().Errorf(ctx, "Error converting HTML to Markdown for %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to convert HTML to Markdown: %w", err)
	}

	return markdown, nil
}

// saveMarkdownToFile saves the Markdown content to a file and returns the filename
func (s *sCrawler) saveMarkdownToFile(ctx context.Context, markdown, pageURL, outputDir string, visitedURLsCount int) (string, error) {
	filename := s.urlToFilename(pageURL, outputDir, visitedURLsCount)

	err := gfile.PutContents(filename, markdown)
	if err != nil {
		s.logger().Errorf(ctx, "Error saving Markdown for %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to save Markdown to file: %w", err)
	}

	s.logger().Debugf(ctx, "Saved Markdown to %s", filename)
	return filename, nil
}

// crawlPage crawls a single page, extracts its content, and follows links to other pages
// It is the main function that orchestrates the crawling process
func (s *sCrawler) crawlPage(
	ctx context.Context,
	pageCtx playwright.BrowserContext,
	pageURL, outputDir string,
	pageToFile *gmap.StrStrMap,
	baseDomainName string,
	visitedURLs map[string]bool,
	depth int,
	maxDepth int,
	maxPages int,
	config *AntiBotConfig,
) {
	// Skip if already visited
	if visitedURLs[pageURL] {
		return
	}
	visitedURLs[pageURL] = true
	if depth > maxDepth {
		s.logger().Debugf(ctx, "current depth %d  over max depth %d then return ", depth, maxDepth)
		return
	}
	if len(visitedURLs) > maxPages {
		s.logger().Debugf(ctx, "current page count %d  over max page count %d then return ", len(visitedURLs), maxPages)
		return
	}

	s.logger().Debugf(ctx, "Crawling: %s", pageURL)

	// 添加隨機延遲以避免被檢測為機器人
	s.randomDelay(ctx, config)

	pageTimeoutCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
	defer cancel()

	// Create a new page with anti-bot settings
	page, err := s.createPage(pageTimeoutCtx, pageCtx, pageURL, config)
	if err != nil {
		return
	}
	defer page.Close()

	// Navigate to the URL with retry mechanism
	if err := s.navigateToURL(pageTimeoutCtx, page, pageURL, config); err != nil {
		// 如果是非網頁內容，記錄信息並跳過，但不視為錯誤
		if err == ErrNonWebContent {
			s.logger().Infof(ctx, "Skipping non-web content URL: %s", pageURL)
			return
		}
		// 其他錯誤則記錄並返回
		s.logger().Errorf(ctx, "Failed to navigate to %s after retries: %v", pageURL, err)
		return
	}

	// Get the page content as Markdown
	markdown, err := s.getPageContent(pageTimeoutCtx, page, pageURL)
	if err != nil {
		return
	}

	// Save the Markdown to a file
	filename, err := s.saveMarkdownToFile(pageTimeoutCtx, markdown, pageURL, outputDir, len(visitedURLs))
	if err != nil {
		return
	}

	// Store the mapping from URL to filename
	pageToFile.Set(pageURL, filename)

	// Extract links from the page
	links, err := s.extractLinks(page, baseDomainName, visitedURLs)
	if err != nil {
		s.logger().Errorf(pageTimeoutCtx, "Error extracting links from %s: %v", pageURL, err)
		return
	}

	// Crawl each link
	for _, link := range links {
		s.crawlPage(pageTimeoutCtx, pageCtx, link, outputDir, pageToFile, baseDomainName, visitedURLs, depth+1, maxDepth, maxPages, config)
		depth = 0
	}
}

// extractLinks extracts all links from a page that belong to the same domain
// and have not been visited yet
func (s *sCrawler) extractLinks(page playwright.Page, domainName string, visitedURLs map[string]bool) ([]string, error) {
	// Validate inputs
	if page == nil {
		return nil, fmt.Errorf("page is nil")
	}
	if domainName == "" {
		return nil, fmt.Errorf("domain name is empty")
	}
	if visitedURLs == nil {
		return nil, fmt.Errorf("visitedURLs map is nil")
	}

	// Execute JavaScript to get all links
	linksObj, err := page.Evaluate(`
		Array.from(document.querySelectorAll('a[href]'))
			.map(a => a.href)
			.filter(href => href.includes('` + domainName + `'))
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate JavaScript: %w", err)
	}

	// Convert the result to a slice of strings
	linksArr, ok := linksObj.([]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected type for links: %T", linksObj)
	}

	var links []string
	for _, linkObj := range linksArr {
		linkStr, ok := linkObj.(string)
		if !ok {
			// Skip non-string links
			continue
		}

		// Normalize the URL
		parsedURL, err := url.Parse(linkStr)
		if err != nil {
			// Skip invalid URLs
			continue
		}

		// Skip fragment links (same page)
		parsedURL.Fragment = ""

		// Skip if not the same domain
		if !strings.Contains(parsedURL.Host, domainName) {
			continue
		}

		normalizedURL := parsedURL.String()

		// Skip if already visited
		if !visitedURLs[normalizedURL] {
			links = append(links, normalizedURL)
		}
	}

	return links, nil
}

// urlToFilename converts a URL to a filename, ensuring it's unique within the output directory
// It handles URL parsing errors, empty paths, and filename collisions
func (s *sCrawler) urlToFilename(pageURL, outputDir string, mapLen int) string {
	// Validate inputs
	if pageURL == "" {
		return gfile.Join(outputDir, fmt.Sprintf("page_%d.md", mapLen))
	}
	if outputDir == "" {
		outputDir = "."
	}

	// Ensure output directory exists
	if !gfile.Exists(outputDir) {
		if err := gfile.Mkdir(outputDir); err != nil {
			s.logger().Errorf(context.Background(), "Failed to create output directory %s: %v", outputDir, err)
			return gfile.Join(outputDir, fmt.Sprintf("page_%d.md", mapLen))
		}
	}

	// Parse the URL
	parsedURL, err := url.Parse(pageURL)
	if err != nil {
		// Fallback to a simple hash if URL parsing fails
		return gfile.Join(outputDir, fmt.Sprintf("page_%d.md", mapLen))
	}

	// Use the path part of the URL for the filename
	urlPath := parsedURL.Path
	if urlPath == "" || urlPath == "/" {
		urlPath = "index"
	} else {
		// Remove leading slash and replace remaining slashes with underscores
		urlPath = strings.TrimPrefix(urlPath, "/")
		urlPath = strings.ReplaceAll(urlPath, "/", "_")
	}

	// Remove query parameters and add .md extension
	filename := urlPath
	if filename == "" {
		filename = "index"
	}

	// Add a suffix if the filename already exists
	baseFilename := gfile.Join(outputDir, filename+".md")
	finalFilename := baseFilename
	counter := 1
	maxAttempts := 1000 // Prevent infinite loop

	for counter < maxAttempts {
		_, err := os.Stat(finalFilename)
		if err != nil {
			if os.IsNotExist(err) {
				// File doesn't exist, we can use this filename
				break
			}
			// Some other error occurred, use a fallback filename
			s.logger().Errorf(context.Background(), "Error checking file existence: %v", err)
			return gfile.Join(outputDir, fmt.Sprintf("%s_%d_%d.md", filename, counter, mapLen))
		}
		// File exists, try with a new counter
		finalFilename = gfile.Join(outputDir, fmt.Sprintf("%s_%d.md", filename, counter))
		counter++
	}

	// If we reached the maximum number of attempts, use a timestamp-based filename
	if counter >= maxAttempts {
		finalFilename = gfile.Join(outputDir, fmt.Sprintf("%s_%d_%d.md", filename, mapLen, time.Now().UnixNano()))
	}

	return finalFilename
}
func (s *sCrawler) CrawlWebSites(ctx context.Context, in *crawl.CrawlWebSiteInput) (err error) {

	s.logger().Infof(ctx, "crawl web site : %v", gjson.New(in).MustToJsonIndentString())
	if in == nil {
		return fmt.Errorf("input is nil")
	}

	if len(in.WebPageUrls) > 0 {
		var chs = make(chan *website.ModelWebSite, len(in.WebPageUrls))
		var counter = 0
		g.Go(gctx.NeverDone(ctx), func(ctx context.Context) {
			var allWebSiteData = make([]*website.ModelWebSite, 0)
			var quit = false
			for {

				select {
				case webSiteData := <-chs:
					if webSiteData != nil {
						allWebSiteData = append(allWebSiteData, webSiteData)
					}

					counter++
					if counter == len(in.WebPageUrls) {
						s.logger().Debugf(ctx, "crawl web sites  finish ...  ")
						quit = true
						break
					}
				}

				if quit {
					break
				}
			}

			if allWebSiteData != nil && len(allWebSiteData) > 0 {

				e := service.DataSaver().SaveURLContent(ctx, &data_saver.SaveWebPageInput{
					BaseInfo:    in.BaseInfo,
					WebSiteURLs: allWebSiteData,
				})

				if e != nil {
					panic(e)
				}
			}

		}, func(ctx context.Context, exception error) {
			s.logger().Error(ctx, exception)
			close(chs)
		})

		for _, siteDataWebSite := range in.WebPageUrls {

			if e := s.workerPool.AddWithRecover(gctx.NeverDone(ctx), func(ctx context.Context) {

				webSite, e := s.crawlWebSite(ctx, &crawl.CrawlingWebSiteInput{
					BaseInfo:   in.BaseInfo,
					WebPageUrl: siteDataWebSite,
				})

				if e != nil {
					chs <- nil
				} else {
					chs <- webSite
				}

			}, func(ctx context.Context, exception error) {
				s.logger().Error(ctx, exception)
			}); e != nil {
				s.logger().Debugf(ctx, "crawling web site %q failed : %v", siteDataWebSite.URL, e)
				chs <- nil
			}
		}
	} else {
		err = gerror.New("the web site data is empty ")
		return
	}

	return

}
func (s *sCrawler) crawlWebSite(ctx context.Context, in *crawl.CrawlingWebSiteInput) (webSiteData *website.ModelWebSite, err error) {
	s.logger().Infof(ctx, "Crawling  website : %v", gjson.New(in).MustToJsonIndentString())

	webSiteData = &website.ModelWebSite{DataWebSite: in.WebPageUrl}

	var pageToFile = gmap.NewStrStrMap()

	defer func() {
		if err == nil {
			webSiteData.WebPageFile = pageToFile.Map()
		}

	}()

	// 載入反爬蟲配置
	config := s.loadAntiBotConfig(ctx)

	// 增強的瀏覽器啟動參數，用於規避反爬蟲檢測
	launchArgs := []string{
		"--disable-blink-features=AutomationControlled",
		"--disable-web-security",
		"--disable-features=VizDisplayCompositor",
		"--disable-background-timer-throttling",
		"--disable-backgrounding-occluded-windows",
		"--disable-renderer-backgrounding",
		"--disable-field-trial-config",
		"--disable-back-forward-cache",
		"--disable-background-networking",
		"--enable-features=NetworkService,NetworkServiceInProcess",
		"--disable-ipc-flooding-protection",
		"--disable-hang-monitor",
		"--disable-client-side-phishing-detection",
		"--disable-popup-blocking",
		"--disable-prompt-on-repost",
		"--disable-sync",
		"--disable-extensions",
		"--no-first-run",
		"--no-default-browser-check",
		"--no-sandbox",
		"--disable-setuid-sandbox",
		"--disable-dev-shm-usage",
		"--disable-accelerated-2d-canvas",
		"--disable-accelerated-jpeg-decoding",
		"--disable-accelerated-mjpeg-decode",
		"--disable-accelerated-video-decode",
		"--disable-gpu",
		"--disable-gpu-sandbox",
		"--disable-software-rasterizer",
		"--disable-background-media-suspend",
	}

	browser, err := s.playWright.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(true),
		Args:     launchArgs,
	})
	if err != nil {
		s.logger().Errorf(ctx, "crawl website launch failed : %v", err)

	} else {
		defer browser.Close()
		var pageCtx playwright.BrowserContext
		var visitedURLs = make(map[string]bool)

		// 創建瀏覽器上下文，設置 User-Agent 和其他屬性
		userAgent := s.getRandomUserAgent(config)
		pageCtx, err = browser.NewContext(playwright.BrowserNewContextOptions{
			UserAgent: playwright.String(userAgent),
			Viewport: &playwright.Size{
				Width:  1920,
				Height: 1080,
			},
			Locale:            playwright.String("zh-TW"),
			TimezoneId:        playwright.String("Asia/Taipei"),
			JavaScriptEnabled: playwright.Bool(true),
		})

		if err != nil {
			s.logger().Error(ctx, "crawl website new context failed : %v", err)
		} else {
			domainUrl := ""
			domainUrl, err = s.extractDomainUrl(in.WebPageUrl.URL)

			vDir, _ := g.Cfg().Get(ctx, "system.temp_location", "./tmp")

			outputDir := gfile.Join(
				vDir.String(),
				in.TenantID,
				in.ServiceID,
				in.UserID,
				gbase64.EncodeString(in.WebPageUrl.URL),
			)

			vMaxDepth, _ := g.Cfg().Get(ctx, "system.crawling.max_depth", 10)
			vMaxPages, _ := g.Cfg().Get(ctx, "system.crawling.max_pages", 50)

			if err != nil {
				s.logger().Error(ctx, "crawl website extract domain url failed : %v", err)
			} else {
				s.crawlPage(ctx, pageCtx, in.WebPageUrl.URL, outputDir, pageToFile, domainUrl, visitedURLs, 0, vMaxDepth.Int(), vMaxPages.Int(), config)
				if !visitedURLs["https://"+domainUrl] {
					s.crawlPage(ctx, pageCtx, "https://"+domainUrl, outputDir, pageToFile, domainUrl, visitedURLs, 0, vMaxDepth.Int(), vMaxPages.Int(), config)

				}

				s.logger().Debugf(ctx, "Crawling completed, visiting %d pages", len(visitedURLs))

			}

		}

	}

	return
}

func (s *sCrawler) Stop(ctx context.Context) {
	if s.playWright != nil {
		_ = s.playWright.Stop()
	}
}
